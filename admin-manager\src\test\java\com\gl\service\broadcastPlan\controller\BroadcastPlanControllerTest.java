package com.gl.service.broadcastPlan.controller;

import com.gl.config.BaseControllerTest;
import com.gl.framework.web.response.Result;
import com.gl.service.broadcastPlan.service.BroadcastPlanService;
import com.gl.service.broadcastPlan.vo.BroadcastPlanInfoVo;
import com.gl.service.broadcastPlan.vo.BroadcastPlanVo;
import com.gl.service.broadcastPlan.vo.dto.BroadcastPlanAddDto;
import com.gl.service.broadcastPlan.vo.dto.BroadcastPlanDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * BroadcastPlanController单元测试类
 * 测试播报计划控制器的所有HTTP端点，包括正面和负面场景
 */
@DisplayName("播报计划控制器测试")
class BroadcastPlanControllerTest extends BaseControllerTest {

        @MockBean
        private BroadcastPlanService broadcastPlanService;

        private BroadcastPlanDto testDto;
        private BroadcastPlanAddDto testAddDto;
        private Result successResult;
        private Result failResult;

        @BeforeEach
        void setUp() {
                // 设置测试数据
                testDto = new BroadcastPlanDto();
                testDto.setSearchCondition("test");

                testAddDto = new BroadcastPlanAddDto();
                testAddDto.setStartTime("09:00");
                testAddDto.setEndTime("18:00");
                testAddDto.setShopId(1L);
                testAddDto.setDeviceIds(Arrays.asList(1L, 2L));
                testAddDto.setVoiceWorkList(Arrays.asList(1L, 2L));

                // 设置成功结果
                successResult = Result.success();
                Map<String, Object> data = new HashMap<>();
                data.put("total", 5);
                data.put("result", createMockBroadcastPlanVoList());
                successResult.addData("total", 5);
                successResult.addData("result", createMockBroadcastPlanVoList());

                // 设置失败结果
                failResult = Result.fail("操作失败");
        }

        @Test
        @DisplayName("测试GET /broadcastPlan - 成功获取播报计划列表")
        @WithMockUser(authorities = "dub:broadcastPlan:list")
        void testList_Success_ShouldReturnPlanList() throws Exception {
                // Given
                when(broadcastPlanService.list(any(BroadcastPlanDto.class), eq(1)))
                                .thenReturn(successResult);

                // When & Then
                mockMvc.perform(get("/broadcastPlan")
                                .param("searchCondition", "test")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"))
                                .andExpect(jsonPath("$.data.total").value(5))
                                .andExpect(jsonPath("$.data.result").isArray());
        }

        @Test
        @DisplayName("测试GET /broadcastPlan - 无权限时返回403")
        @WithMockUser(authorities = "other:permission")
        void testList_NoPermission_ShouldReturn403() throws Exception {
                // When & Then
                mockMvc.perform(get("/broadcastPlan")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isForbidden());
        }

        @Test
        @DisplayName("测试GET /broadcastPlan - 服务异常时返回错误")
        @WithMockUser(authorities = "dub:broadcastPlan:list")
        void testList_ServiceException_ShouldReturnError() throws Exception {
                // Given
                when(broadcastPlanService.list(any(BroadcastPlanDto.class), eq(1)))
                                .thenReturn(failResult);

                // When & Then
                mockMvc.perform(get("/broadcastPlan")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("操作失败"));
        }

        @Test
        @DisplayName("测试POST /broadcastPlan - 成功创建播报计划")
        @WithMockUser(authorities = "dub:broadcastPlan:add")
        void testAddOrUpdate_Create_Success() throws Exception {
                // Given
                testAddDto.setId(null); // 新增
                when(broadcastPlanService.addOrUpdate(any(BroadcastPlanAddDto.class)))
                                .thenReturn(Result.success());

                // When & Then
                mockMvc.perform(post("/broadcastPlan")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testAddDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"));
        }

        @Test
        @DisplayName("测试POST /broadcastPlan - 成功更新播报计划")
        @WithMockUser(authorities = "dub:broadcastPlan:add")
        void testAddOrUpdate_Update_Success() throws Exception {
                // Given
                testAddDto.setId(1L); // 更新
                when(broadcastPlanService.addOrUpdate(any(BroadcastPlanAddDto.class)))
                                .thenReturn(Result.success());

                // When & Then
                mockMvc.perform(post("/broadcastPlan")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testAddDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"));
        }

        @Test
        @DisplayName("测试DELETE /broadcastPlan - 成功删除播报计划")
        @WithMockUser(authorities = "dub:broadcastPlan:delete")
        void testDelete_Success() throws Exception {
                // Given
                BroadcastPlanAddDto deleteDto = new BroadcastPlanAddDto();
                deleteDto.setIds(Arrays.asList(1L, 2L));

                when(broadcastPlanService.delete(any(BroadcastPlanAddDto.class)))
                                .thenReturn(Result.success());

                // When & Then
                mockMvc.perform(delete("/broadcastPlan")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(deleteDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"));
        }

        @Test
        @DisplayName("测试GET /broadcastPlan/{id} - 成功获取播报计划详情")
        @WithMockUser
        void testGetInfo_Success() throws Exception {
                // Given
                Long planId = 1L;
                BroadcastPlanInfoVo infoVo = createMockBroadcastPlanInfoVo();
                when(broadcastPlanService.getInfo(planId))
                                .thenReturn(Result.success(infoVo));

                // When & Then
                mockMvc.perform(get("/broadcastPlan/{id}", planId)
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"))
                                .andExpect(jsonPath("$.data.id").value(1));
        }

        @Test
        @DisplayName("测试GET /broadcastPlan/{id} - 播报计划不存在")
        @WithMockUser
        void testGetInfo_NotFound() throws Exception {
                // Given
                Long planId = 999L;
                when(broadcastPlanService.getInfo(planId))
                                .thenReturn(Result.fail("播报计划不存在"));

                // When & Then
                mockMvc.perform(get("/broadcastPlan/{id}", planId)
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("播报计划不存在"));
        }

        /**
         * 创建模拟的BroadcastPlanVo列表
         */
        private ArrayList<BroadcastPlanVo> createMockBroadcastPlanVoList() {
                ArrayList<BroadcastPlanVo> list = new ArrayList<>();
                BroadcastPlanVo vo = new BroadcastPlanVo();
                vo.setId(1L);
                vo.setStartTime("09:00");
                vo.setEndTime("18:00");
                vo.setShopName("测试门店");
                list.add(vo);
                return list;
        }

        /**
         * 创建模拟的BroadcastPlanInfoVo对象
         */
        private BroadcastPlanInfoVo createMockBroadcastPlanInfoVo() {
                BroadcastPlanInfoVo infoVo = new BroadcastPlanInfoVo();
                infoVo.setId(1L);
                infoVo.setStartTime("09:00");
                infoVo.setEndTime("18:00");
                infoVo.setShopName("测试门店");
                infoVo.setVoiceWorkRefList(new ArrayList<>());
                return infoVo;
        }
}